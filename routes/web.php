<?php

use App\Http\Controllers\Admin\ActivityController;
use App\Http\Controllers\Admin\AlertController;
use App\Http\Controllers\Admin\Auth\LoginController;
use App\Http\Controllers\Admin\BankController;
use App\Http\Controllers\Admin\CardController;
use App\Http\Controllers\Admin\CoinController;
use App\Http\Controllers\Admin\CurrencyController;
use App\Http\Controllers\Admin\DashboardController;
use App\Http\Controllers\Admin\DepositController;
use App\Http\Controllers\Admin\DocumentController;
use App\Http\Controllers\Admin\FrequencyQuestionController;
use App\Http\Controllers\Admin\FrequencyQuestionGroupController;
use App\Http\Controllers\Admin\JibitController;
use App\Http\Controllers\Admin\LevelsController;
use App\Http\Controllers\Admin\NetworkController;
use App\Http\Controllers\Admin\RoleController;
use App\Http\Controllers\Admin\SettingController;
use App\Http\Controllers\Admin\SettlementController;
use App\Http\Controllers\Admin\SettlementInfoController;
use App\Http\Controllers\Admin\SupportController;
use App\Http\Controllers\Admin\SystemWalletController;
use App\Http\Controllers\Admin\TicketController;
use App\Http\Controllers\Admin\TomanWithdrawalController;
use App\Http\Controllers\Admin\TransactionController;
use App\Http\Controllers\Admin\UsdPriceController;
use App\Http\Controllers\Admin\User\UserController;
use App\Http\Controllers\Admin\WalletController;
use Illuminate\Support\Facades\Route;

Route::post('logout', [LoginController::class, 'logout'])->name('admin.logout');
Route::get('login', [LoginController::class, 'showLoginForm'])->name('admin.login');
Route::get('login/user', [LoginController::class, 'showLoginForm'])->name('login');
Route::post('login', [LoginController::class, 'login'])->name('admin.login.post');
Route::get('captcha/refresh', [LoginController::class, 'refreshCaptcha'])->name('admin.captcha.refresh');

Route::prefix('admin')->name('admin.')->middleware(['auth','ensure.has.role'])->group(function () {
    // Dashboard
    Route::get('/', [DashboardController::class, 'index'])->name('dashboard');
    Route::get('/api/daily-trading-stats', [DashboardController::class, 'getDailyTradingStatsApi'])->name('dashboard.daily-trading-stats');

    // Users
    Route::prefix('users')->name('users.')->group(function () {
        Route::get('/', [UserController::class, 'index'])->name('index');
        Route::get('/quick-search', [UserController::class, 'quickSearch'])->name('quick-search');
        Route::get('/create', [UserController::class, 'create'])->name('create');
        Route::post('/', [UserController::class, 'store'])->name('store');
        Route::get('/{id}', [UserController::class, 'show'])->name('show');
        Route::get('/{id}/edit', [UserController::class, 'edit'])->name('edit');
        Route::put('/{id}', [UserController::class, 'update'])->name('update');
        Route::delete('/{id}', [UserController::class, 'destroy'])->name('destroy');
        Route::post('/create-wallet/coin/{id}', [UserController::class, 'CreateWalletbyCoin'])->name('CreateWalletbyCoin');
        
        // Level management routes
        Route::get('/{id}/level-info', [UserController::class, 'getUserLevelInfo'])->name('level-info');
        Route::post('/{id}/upgrade-level', [UserController::class, 'upgradeLevel'])->name('upgrade-level');
        Route::post('/{id}/downgrade-level', [UserController::class, 'downgradeLevel'])->name('downgrade-level');
    });
    Route::prefix('wallet')->name('wallet.')->group(function () {
        Route::controller(WalletController::class)->group(function () {
            Route::get('', 'index')->name('index');
            Route::get('/create/{userId?}', 'create')->name('create');
            Route::post('', 'store')->name('store');
            Route::get('{id}', 'show')->name('show');
            Route::post('{id}', 'update')->name('update');
            Route::delete('{id}', 'destroy')->name('destroy');
        });
    });
    // Transaction routes moved to 'transactions' prefix
    // Roles
    Route::prefix('roles')->name('roles.')->group(function () {
        Route::get('/', [RoleController::class, 'index'])->name('index');
        Route::get('/create', [RoleController::class, 'create'])->name('create');
        Route::post('/', [RoleController::class, 'store'])->name('store');
        Route::get('/{role}/edit', [RoleController::class, 'edit'])->name('edit');
        Route::put('/{role}', [RoleController::class, 'update'])->name('update');
        Route::delete('/{role}', [RoleController::class, 'destroy'])->name('destroy');
    });
    // Activity Logs
    Route::prefix('activities')->name('activities.')->group(function(){
        Route::controller(ActivityController::class)->group(function(){
            Route::get('', 'index')->name('index');
            Route::get('{id}', 'show')->name('show');
        });
    });
    // Support
    Route::prefix('support')->name('support.')->group(function () {
        Route::get('/', [SupportController::class, 'index'])->name('index');
        Route::get('/create', [SupportController::class, 'create'])->name('create');
        Route::post('/', [SupportController::class, 'store'])->name('store');
        Route::get('/{id}', [SupportController::class, 'show'])->name('show');
        Route::get('/{id}/edit', [SupportController::class, 'edit'])->name('edit');
        Route::put('/{id}', [SupportController::class, 'update'])->name('update');
        Route::delete('/{id}', [SupportController::class, 'destroy'])->name('destroy');
    });

    // Wallets
    Route::prefix('wallets')->name('wallets.')->group(function () {
        Route::get('/', [WalletController::class, 'index'])->name('index');
        Route::get('/create', [WalletController::class, 'create'])->name('create');
        Route::post('/', [WalletController::class, 'store'])->name('store');
        Route::get('/{id}', [WalletController::class, 'show'])->name('show');
        Route::get('/{id}/edit', [WalletController::class, 'edit'])->name('edit');
        Route::put('/{id}', [WalletController::class, 'update'])->name('update');
        Route::delete('/{id}', [WalletController::class, 'destroy'])->name('destroy');
        Route::post('adjust-balance/{wallet}', [WalletController::class,'adjustBalance'])->name('adjust-balance');
    });

    // Tickets
    Route::prefix('tickets')->name('tickets.')->group(function () {
        Route::get('/', [TicketController::class, 'index'])->name('index');
        Route::get('{id}', [TicketController::class, 'show'])->name('show');
        Route::post('{id}/reply', [TicketController::class, 'reply'])->name('reply');
    });
    // Documents
    Route::prefix('documents')->name('documents.')->group(function () {
        Route::get('/', [DocumentController::class, 'index'])->name('index');
        Route::get('/grouped', [DocumentController::class, 'grouped'])->name('grouped');
        Route::get('/{id}', [DocumentController::class, 'show'])->name('show');
        Route::get('/{id}/edit', [DocumentController::class, 'edit'])->name('edit');
        Route::put('/{id}', [DocumentController::class, 'update'])->name('update');
        Route::get('/{document}/file', [DocumentController::class, 'file'])->name('file');
    });

    // Transactions
    Route::prefix('transactions')->name('transaction.')->group(function () {
        Route::get('/', [TransactionController::class, 'index'])->name('index');
        Route::get('/filter', [TransactionController::class, 'index'])->name('filter');
        Route::get('/{id}', [TransactionController::class, 'show'])->name('show');
        Route::post('/{id}', [TransactionController::class, 'update'])->name('update');
        Route::get('/type/{type}', [TransactionController::class, 'indexByType'])->name('by-type');
        Route::get('/status/{status}', [TransactionController::class, 'indexByStatus'])->name('by-status');
        Route::get('/user/{userId}', [TransactionController::class, 'indexByUser'])->name('by-user');
        Route::get('/currency/{currencyId}', [TransactionController::class, 'indexByCurrency'])->name('by-currency');
        Route::get('/export', [TransactionController::class, 'export'])->name('export');
    });

    // Settings
    Route::prefix('settings')->name('settings.')->group(function () {
        Route::get('/', [SettingController::class, 'index'])->name('index');
        Route::get('/create', [SettingController::class, 'create'])->name('create');
        Route::post('/', [SettingController::class, 'store'])->name('store');
        Route::get('/{id}/edit', [SettingController::class, 'edit'])->name('edit');
        Route::put('/{id}', [SettingController::class, 'update'])->name('update');

        // Levels
        Route::prefix('levels')->name('levels.')->group(function () {
            Route::get('/', [LevelsController::class, 'index'])->name('index');
            Route::get('/create', [LevelsController::class, 'create'])->name('create');
            Route::post('/', [LevelsController::class, 'store'])->name('store');
            Route::get('/{id}/edit', [LevelsController::class, 'edit'])->name('edit');
            Route::put('/{id}', [LevelsController::class, 'update'])->name('update');
            
            // // API routes for user level management
            // Route::post('users/{userId}/upgrade-after-document-approval', 'upgradeUserAfterDocumentApproval')->name('upgrade-user-after-document');
            // Route::get('users/{userId}/upgrade-status', 'getUserUpgradeStatus')->name('user-upgrade-status');
        });
    });
    Route::prefix('referrals')->name('referrals.')->group(function () {
        Route::get('/', [\App\Http\Controllers\Admin\AdminReferralController::class, 'index'])->name('index');
        Route::get('/{user}', [\App\Http\Controllers\Admin\AdminReferralController::class, 'show'])->name('show');
    }); 
    // FAQ
    Route::prefix('faq')->name('faq.')->group(function () {
        Route::get('/', [FrequencyQuestionController::class, 'index'])->name('index');
        Route::get('/create', [FrequencyQuestionController::class, 'create'])->name('create');
        Route::post('/', [FrequencyQuestionController::class, 'store'])->name('store');
        Route::get('/{id}/edit', [FrequencyQuestionController::class, 'edit'])->name('edit');
        Route::put('/{id}', [FrequencyQuestionController::class, 'update'])->name('update');
        Route::delete('/{id}', [FrequencyQuestionController::class, 'destroy'])->name('destroy');

        Route::resource('groups', FrequencyQuestionGroupController::class);
    });
    // System Wallets
    Route::prefix('system-wallets')->name('system-wallets.')->group(function () {
        Route::get('/', [SystemWalletController::class, 'index'])->name('index');
        Route::get('/create', [SystemWalletController::class, 'create'])->name('create');
        Route::post('/', [SystemWalletController::class, 'store'])->name('store');
        Route::get('/{id}/edit', [SystemWalletController::class, 'edit'])->name('edit');
        Route::put('/{id}', [SystemWalletController::class, 'update'])->name('update');
        Route::post('/toggle-status', [SystemWalletController::class, 'toggleStatus'])->name('toggle-status');
        Route::post('/get-balance', [SystemWalletController::class, 'getBalance'])->name('get-balance');
        Route::get('/user-wallets', [SystemWalletController::class, 'userWallets'])->name('user-wallets');
        Route::post('/user-wallets', [SystemWalletController::class, 'userWallets'])->name('user-wallets.post');
    });
    // Activity Logs
    Route::get('/activities', [ActivityController::class, 'index'])->name('activities.index');
    // Networks
    Route::prefix('networks')->name('networks.')->group(function () {
        Route::get('/', [NetworkController::class, 'index'])->name('index');
        Route::get('/create', [NetworkController::class, 'create'])->name('create');
        Route::post('/', [NetworkController::class, 'store'])->name('store');
        Route::get('/{id}/edit', [NetworkController::class, 'edit'])->name('edit');
        Route::put('/{id}', [NetworkController::class, 'update'])->name('update');
        Route::get('/{id}/delete', [NetworkController::class, 'delete'])->name('delete');
        Route::post('/toggle-status', [NetworkController::class, 'toggleStatus'])->name('toggle-status');
        Route::post('/check-block', [NetworkController::class, 'checkCurrentBlock'])->name('check-block');
    });

    // Coin Network
    Route::controller(\App\Http\Controllers\admin\CoinNetworkController::class)->group(function () {
        Route::get('coin-network-list', 'getCoinNetworkList')->name('getCoinNetworkList');
        Route::get('create-coin-network/{id?}', 'createCoinNetwork')->name('createCoinNetwork');
        Route::post('create-coin-network-proccess', 'createCoinNetworkProccess')->name('createCoinNetworkProccess');
        Route::post('change-coin-network-status', 'changeCoinNetworkStatus')->name('changeCoinNetworkStatus');
        Route::get('coin-network-settings/{id}', 'coinNetworkSettings')->name('coinNetworkSettings');
        Route::get('coin-network-delete/{id}', 'coinNetworkDelete')->name('coinNetworkDelete');
    });
    // Coins
    Route::prefix('coins')->name('coins.')->group(function () {
        Route::get('/', [CoinController::class, 'index'])->name('index');
        Route::get('/create', [CoinController::class, 'create'])->name('create');
        Route::post('/', [CoinController::class, 'store'])->name('store');
        Route::get('/{coin}', [CoinController::class, 'show'])->name('show');
        Route::get('/{coin}/edit', [CoinController::class, 'edit'])->name('edit');
        Route::put('/{coin}', [CoinController::class, 'update'])->name('update');
        Route::delete('/{coin}', [CoinController::class, 'destroy'])->name('destroy');
    });
    // Deposits
    Route::prefix('deposit')->name('deposit.')->group(function () {
        Route::controller(DepositController::class)->group(function () {
            Route::get('check', 'adminCheckDeposit')->name('check');
            Route::get('submit-check', 'submitCheckDeposit')->name('submitCheckDeposit');
            Route::get('get-currency/{network}', 'getCurrencyByNetworkID')->name('getCurrencyByNetworkID');
            Route::get('pending-history', 'adminPendingDepositHistory')->name('pending-history');
            Route::get('ico-token-buy-list-accept', 'icoTokenBuyListAccept')->name('icoTokenBuyListAccept');
            //adminPendingDepositAccept
            Route::get('admin-pending-deposit-accept/{id}', 'adminPendingDepositAccept')->name('adminPendingDepositAccept');
        });
    });

    // Withdrawals
    Route::prefix('withdrawal')->name('withdrawal.')->group(function () {
        Route::controller(\App\Http\Controllers\Admin\Transaction\TransactionController::class)->group(function () {
            Route::get('pending', 'adminPendingWithdrawal')->name('adminPendingWithdrawal');
            Route::get('rejected', 'adminRejectedWithdrawal')->name('adminRejectedWithdrawal');
            Route::get('active', 'adminActiveWithdrawal')->name('adminActiveWithdrawal');
            Route::get('accept/{id}', 'adminAcceptPendingWithdrawal')->name('adminAcceptPendingWithdrawal');
            Route::get('reject/{id}', 'adminRejectPendingWithdrawal')->name('adminRejectPendingWithdrawal');
            Route::get('details/{id}', 'adminWithdrawalDetails')->name('adminWithdrawalDetails');
            Route::get('transaction-history-export', 'adminTransactionHistoryExport')->name('adminTransactionHistoryExport');
            Route::get('manual-accept/{id}', 'manualAcceptPendingWithdrawal')->name('manualAcceptPendingWithdrawal');
        });
    });
        // Bank Management Routes
        Route::prefix('banks')->name('banks.')->group(function () {
            Route::controller(BankController::class)->group(function () {
                Route::get('', 'index')->name('index');
                Route::get('create', 'create')->name('create');
                Route::post('', 'store')->name('store');
                Route::get('{bank}/edit', 'edit')->name('edit');
                Route::put('{bank}', 'update')->name('update');
                Route::delete('{bank}', 'destroy')->name('destroy');
                Route::post('toggle-status', 'toggleStatus')->name('toggle-status');
            });
        });
    Route::prefix('document')->name('document.')->group(function () {
        Route::controller(DocumentController::class)->group(function () {
            Route::get('', 'index')->name('index');
            Route::get('{id}', 'show')->name('show');
            Route::post('{id}', 'update')->name('update');
            Route::post('bulk-approve-user/{userId}', 'bulkApproveUser')->name('bulk-approve-user');
            Route::post('bulk-reject-user/{userId}', 'bulkRejectUser')->name('bulk-reject-user');
        });
    });
    Route::prefix('accounting')->name('accounting.')->group(function () {
        Route::prefix('settlement')->name('settlement.')->group(function () {
            Route::prefix('info')->name('info.')->group(function () {
                Route::controller(SettlementInfoController::class)->group(function () {
                    Route::get('', 'index')->name('index');
                    Route::post('', 'store')->name('store');
                });
            });
            Route::controller(SettlementController::class)->group(function () {
                Route::get('', 'index')->name('index');
                Route::post('', 'store')->name('store');
                Route::get('{id}', 'show')->name('show');
                Route::post('{id}', 'update')->name('update');
            });
        });

        Route::prefix('toman-withdrawal')->name('toman-withdrawal.')->group(function () {
            Route::controller(TomanWithdrawalController::class)->group(function () {
                Route::get('pending', 'pending')->name('pending');
                Route::get('approved', 'approved')->name('approved');
                Route::get('rejected', 'rejected')->name('rejected');
                Route::post('approve/{id}', 'approve')->name('approve');
                Route::post('reject/{id}', 'reject')->name('reject');
            });
        });
    });

    Route::prefix('setting')->name('setting.')->group(function () {
        Route::prefix('level')->name('level.')->group(function () {
            Route::controller(LevelsController::class)->group(function () {
                Route::get('', 'index')->name('index');
                Route::post('', 'store')->name('store');
                Route::get('{id}', 'show')->name('show');
                Route::post('{id}', 'update')->name('update');
                
                // API routes for user level management
                Route::post('users/{userId}/upgrade-after-document-approval', 'upgradeUserAfterDocumentApproval')->name('upgrade-user-after-document');
                Route::get('users/{userId}/upgrade-status', 'getUserUpgradeStatus')->name('user-upgrade-status');
            });
            // Route::prefix(prefix: 'item')->name('item.')->group(function(){
            //     Route::controller(SettingController::class)->group(function () {
            //         Route::get('', 'index')->name('index');
            //         Route::post('', 'store')->name('store');
            //         Route::get('{id}', 'show')->name('show');
            //         Route::post('{id}', 'update')->name('update');
            //     });
            // });
            // Route::prefix(prefix: 'type')->name('type.')->group(function(){
            //     Route::controller(SettingController::class)->group(function () {
            //         Route::get('', 'index')->name('index');
            //         Route::post('', 'store')->name('store');
            //         Route::get('{id}', 'show')->name('show');
            //         Route::post('{id}', 'update')->name('update');
            //     });
            // });
        });
        Route::controller(SettingController::class)->group(function () {
            Route::get('', 'index')->name('index');
            Route::post('', 'store')->name('store');
            Route::get('{id}', 'show')->name('show');
            Route::post('{id}', 'update')->name('update');
        });
    });
    Route::prefix('toman-withdrawal')->name('toman-withdrawal.')->group(function () {
        Route::controller(TomanWithdrawalController::class)->group(function () {
            Route::get('pending', 'pending')->name('pending');
            Route::get('approved', 'approved')->name('approved');
            Route::get('rejected', 'rejected')->name('rejected');
            Route::get('operation/{id}', 'operation')->name('operation');
            Route::get('show/{id}', 'show')->name('show');
            Route::post('approve/{id}', 'approve')->name('approve');
            Route::post('reject/{id}', 'reject')->name('reject');
        });
    });
    // Route::prefix('accounting')->group(function () {
    //     Route::apiResource('toman-withdrawal', TomanWithdrawalController::class)->only(['index', 'store', 'show']);
    // });
    Route::prefix('card')->name('card.')->group(function () {
        Route::controller(CardController::class)->group(function () {
            Route::get('', 'index')->name('index');
            Route::post('', 'store')->name('store');
            Route::get('{id}', 'show')->name('show');
            Route::post('{id}', 'update')->name('update');
            Route::delete('{id}', 'destroy')->name('destroy');
        });
    });

    Route::post('/users/{user}/create-wallet', [WalletController::class, 'createWallet'])
    ->name('users.create-wallet');

    // USD Price Management
    Route::prefix('usd-prices')->name('usd-prices.')->group(function () {
        Route::get('/', [UsdPriceController::class, 'index'])->name('index');
        Route::get('/create', [UsdPriceController::class, 'create'])->name('create');
        Route::post('/', [UsdPriceController::class, 'store'])->name('store');
        Route::get('/{usdPrice}/edit', [UsdPriceController::class, 'edit'])->name('edit');
        Route::put('/{usdPrice}', [UsdPriceController::class, 'update'])->name('update');
        Route::delete('/{usdPrice}', [UsdPriceController::class, 'destroy'])->name('destroy');
        Route::post('/{usdPrice}/set-active', [UsdPriceController::class, 'setActive'])->name('set-active');
    });

    // Alerts Management
    Route::prefix('alerts')->name('alerts.')->group(function () {
        Route::get('/', [AlertController::class, 'index'])->name('index');
        Route::get('/create', [AlertController::class, 'create'])->name('create');
        Route::post('/', [AlertController::class, 'store'])->name('store');
        Route::get('/{id}', [AlertController::class, 'show'])->name('show');
        Route::post('/{id}/mark-as-read', [AlertController::class, 'markAsRead'])->name('mark-as-read');
        Route::post('/{id}/mark-as-unread', [AlertController::class, 'markAsUnread'])->name('mark-as-unread');
        Route::delete('/{id}', [AlertController::class, 'destroy'])->name('destroy');
        Route::post('/mark-all-as-read', [AlertController::class, 'markAllAsRead'])->name('mark-all-as-read');
    });
    Route::prefix('jibit')->name('jibit.')->group(function () {
        Route::get('waiting-labeled-deposits', [JibitController::class, 'waitingLabeledDeposits'])->name('waiting_labeled_deposits');
        Route::post('approve-aug-transaction', [JibitController::class, 'approveAugTransaction'])->name('approve_aug_transaction');
        Route::post('reject-aug-transaction', [JibitController::class, 'rejectAugTransaction'])->name('reject_aug_transaction');
        Route::get('bank-accounts', [JibitController::class, 'bankAccountsPage'])->name('bank_accounts');

    });
    Route::resource('sms-templates', \App\Http\Controllers\Admin\SmsTemplateController::class);
    Route::prefix('blog')->name('blog.')->group(function () {
        Route::resource('posts', \App\Http\Controllers\Admin\BlogPostController::class);
        Route::resource('categories', \App\Http\Controllers\Admin\BlogCategoryController::class);
        Route::resource('tags', \App\Http\Controllers\Admin\BlogTagController::class);
    });
});

Route::prefix('admin/withdrawal')->name('admin.withdrawal.')->middleware(['auth','ensure.has.role'])->group(function () {
    Route::get('qr-code', [\App\Http\Controllers\Admin\Transaction\TransactionController::class, 'qrCodeSvg'])->name('qrCodeSvg');
});

