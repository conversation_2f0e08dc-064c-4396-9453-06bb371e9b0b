@extends('admin.layouts.app')

@section('title', 'ویرایش ادمین')

@section('breadcrumb')
<nav aria-label="breadcrumb">
    <ol class="breadcrumb">
        <li class="breadcrumb-item"><a href="{{ route('admin.dashboard') }}">داشبورد</a></li>
        <li class="breadcrumb-item"><a href="{{ route('admin.admin-management.index') }}">مدیریت ادمین‌ها</a></li>
        <li class="breadcrumb-item active">ویرایش ادمین</li>
    </ol>
</nav>
@endsection

@section('content')
<div class="container-fluid">
    <!-- Header -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <div>
            <h1 class="h3 mb-0 text-gray-800">ویرایش ادمین</h1>
            <p class="text-muted">ویرایش اطلاعات {{ $admin->firstname }} {{ $admin->lastname }}</p>
        </div>
        <div class="d-flex gap-2">
            <a href="{{ route('admin.admin-management.permissions', $admin->id) }}" class="btn btn-info">
                <i class="fas fa-key me-2"></i>مدیریت دسترسی‌ها
            </a>
            <a href="{{ route('admin.admin-management.index') }}" class="btn btn-secondary">
                <i class="fas fa-arrow-right me-2"></i>بازگشت به لیست
            </a>
        </div>
    </div>

    <div class="row">
        <div class="col-lg-8">
            <div class="card">
                <div class="card-header">
                    <h6 class="m-0 font-weight-bold text-primary">ویرایش اطلاعات ادمین</h6>
                </div>
                <div class="card-body">
                    <form method="POST" action="{{ route('admin.admin-management.update', $admin->id) }}">
                        @csrf
                        @method('PUT')
                        
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="firstname" class="form-label">نام <span class="text-danger">*</span></label>
                                    <input type="text" class="form-control @error('firstname') is-invalid @enderror" 
                                           id="firstname" name="firstname" value="{{ old('firstname', $admin->firstname) }}" required>
                                    @error('firstname')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="lastname" class="form-label">نام خانوادگی <span class="text-danger">*</span></label>
                                    <input type="text" class="form-control @error('lastname') is-invalid @enderror" 
                                           id="lastname" name="lastname" value="{{ old('lastname', $admin->lastname) }}" required>
                                    @error('lastname')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="email" class="form-label">ایمیل <span class="text-danger">*</span></label>
                                    <input type="email" class="form-control @error('email') is-invalid @enderror" 
                                           id="email" name="email" value="{{ old('email', $admin->email) }}" required>
                                    @error('email')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="phone" class="form-label">شماره تلفن <span class="text-danger">*</span></label>
                                    <input type="text" class="form-control @error('phone') is-invalid @enderror" 
                                           id="phone" name="phone" value="{{ old('phone', $admin->phone) }}" required>
                                    @error('phone')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="national_id" class="form-label">کد ملی</label>
                                    <input type="text" class="form-control @error('national_id') is-invalid @enderror" 
                                           id="national_id" name="national_id" value="{{ old('national_id', $admin->national_id) }}">
                                    @error('national_id')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="role_id" class="form-label">نقش <span class="text-danger">*</span></label>
                                    <select class="form-select @error('role_id') is-invalid @enderror" 
                                            id="role_id" name="role_id" required {{ $admin->super_admin ? 'disabled' : '' }}>
                                        <option value="">انتخاب کنید...</option>
                                        @foreach($roles as $role)
                                            <option value="{{ $role->id }}" 
                                                {{ old('role_id', $currentRoleId) == $role->id ? 'selected' : '' }}>
                                                {{ $role->name }}
                                            </option>
                                        @endforeach
                                    </select>
                                    @if($admin->super_admin)
                                        <div class="form-text text-warning">
                                            <i class="fas fa-exclamation-triangle me-1"></i>
                                            نقش سوپر ادمین قابل تغییر نیست
                                        </div>
                                        <input type="hidden" name="role_id" value="{{ $currentRoleId }}">
                                    @endif
                                    @error('role_id')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="password" class="form-label">رمز عبور جدید</label>
                                    <input type="password" class="form-control @error('password') is-invalid @enderror" 
                                           id="password" name="password">
                                    <div class="form-text">برای تغییر رمز عبور، رمز جدید را وارد کنید. در غیر این صورت خالی بگذارید.</div>
                                    @error('password')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="password_confirmation" class="form-label">تکرار رمز عبور جدید</label>
                                    <input type="password" class="form-control" 
                                           id="password_confirmation" name="password_confirmation">
                                </div>
                            </div>
                        </div>

                        <div class="mb-3">
                            <label for="status" class="form-label">وضعیت <span class="text-danger">*</span></label>
                            <select class="form-select @error('status') is-invalid @enderror" 
                                    id="status" name="status" required {{ $admin->super_admin ? 'disabled' : '' }}>
                                <option value="approved" {{ old('status', $admin->status) == 'approved' ? 'selected' : '' }}>فعال</option>
                                <option value="rejected" {{ old('status', $admin->status) == 'rejected' ? 'selected' : '' }}>غیرفعال</option>
                            </select>
                            @if($admin->super_admin)
                                <div class="form-text text-warning">
                                    <i class="fas fa-exclamation-triangle me-1"></i>
                                    وضعیت سوپر ادمین قابل تغییر نیست
                                </div>
                                <input type="hidden" name="status" value="{{ $admin->status }}">
                            @endif
                            @error('status')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>

                        <div class="d-flex justify-content-end gap-2">
                            <a href="{{ route('admin.admin-management.index') }}" class="btn btn-secondary">انصراف</a>
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-save me-2"></i>بروزرسانی اطلاعات
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>

        <div class="col-lg-4">
            <div class="card">
                <div class="card-header">
                    <h6 class="m-0 font-weight-bold text-info">اطلاعات ادمین</h6>
                </div>
                <div class="card-body">
                    <div class="text-center mb-3">
                        <div class="avatar-circle mx-auto mb-2" style="width: 60px; height: 60px; font-size: 20px;">
                            {{ substr($admin->firstname, 0, 1) }}{{ substr($admin->lastname, 0, 1) }}
                        </div>
                        <h6>{{ $admin->firstname }} {{ $admin->lastname }}</h6>
                        @if($admin->super_admin)
                            <span class="badge bg-danger">سوپر ادمین</span>
                        @endif
                    </div>

                    <hr>

                    <div class="mb-2">
                        <strong>شناسه:</strong> #{{ $admin->id }}
                    </div>
                    <div class="mb-2">
                        <strong>تاریخ ایجاد:</strong> {{ jdate($admin->created_at)->format('Y/m/d H:i') }}
                    </div>
                    <div class="mb-2">
                        <strong>آخرین بروزرسانی:</strong> {{ jdate($admin->updated_at)->format('Y/m/d H:i') }}
                    </div>
                    <div class="mb-2">
                        <strong>وضعیت فعلی:</strong> 
                        @if($admin->status === 'active')
                            <span class="badge bg-success">فعال</span>
                        @else
                            <span class="badge bg-danger">غیرفعال</span>
                        @endif
                    </div>
                </div>
            </div>

            <div class="card mt-3">
                <div class="card-header">
                    <h6 class="m-0 font-weight-bold text-warning">راهنما</h6>
                </div>
                <div class="card-body">
                    <div class="alert alert-warning">
                        <h6><i class="fas fa-exclamation-triangle me-2"></i>نکات مهم:</h6>
                        <ul class="mb-0">
                            <li>برای تغییر رمز عبور، رمز جدید را وارد کنید</li>
                            <li>سوپر ادمین‌ها قابل ویرایش محدود هستند</li>
                            <li>تغییر نقش بر دسترسی‌ها تأثیر می‌گذارد</li>
                            <li>ادمین‌های غیرفعال نمی‌توانند وارد شوند</li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
.avatar-circle {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-weight: bold;
    font-size: 14px;
}

.form-label {
    font-weight: 600;
    color: #5a5c69;
}

.card-header h6 {
    color: #5a5c69;
}
</style>
@endsection

@push('scripts')
<script>
document.addEventListener('DOMContentLoaded', function() {
    const passwordField = document.getElementById('password');
    const confirmPasswordField = document.getElementById('password_confirmation');

    passwordField.addEventListener('input', function() {
        if (this.value) {
            confirmPasswordField.required = true;
        } else {
            confirmPasswordField.required = false;
            confirmPasswordField.value = '';
        }
    });
});
</script>
@endpush
